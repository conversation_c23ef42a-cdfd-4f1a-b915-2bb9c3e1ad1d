import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NotifResult, UnviredCordovaSDK, UnviredCredential } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { filter, Observable, switchMap, take, tap, map } from 'rxjs';
import { Store } from '@ngrx/store';
import { selectAllForms, selectAllNotifications, selectAllTemplates, selectRigData, selectRigLoadedFromDb } from 'src/app/store/store.selector';
import { TEMPLATE_HEADER } from 'src/models/TEMPLATE_HEADER';
import * as RigActions from 'src/app/store/store.actions';
import { IonButton, IonButtons, IonCol, IonContent, IonGrid, IonHeader, IonItem, IonItemDivider, IonLabel, IonList, IonMenuButton, IonRouterOutlet, IonRow, IonSearchbar, IonThumbnail, IonTitle, IonToggle, IonToolbar, MenuController, Platform } from '@ionic/angular/standalone';
import { DataService } from 'src/app/services/data.service';
import { FORM_HEADER } from 'src/models/FORM_HEADER';
import { CustomDatePipe } from 'src/app/pipes/custom-date.pipe';
import { TranslateModule } from '@ngx-translate/core';
import { BehaviorSubject } from 'rxjs';
import { File } from '@awesome-cordova-plugins/file/ngx';
import { UtilityService } from 'src/app/services/utility.service';
import { InAppBrowser, InAppBrowserObject } from '@awesome-cordova-plugins/in-app-browser/ngx';
import { WebView } from '@awesome-cordova-plugins/ionic-webview/ngx';
import { unzipSync } from 'fflate';
@Component({
  selector: 'app-forms',
  templateUrl: './forms.page.html',
  styleUrls: ['./forms.page.scss'],
  standalone: true,
  imports: [IonRow, IonCol, IonGrid,  IonItemDivider, IonThumbnail, IonSearchbar, IonToggle, TranslateModule, CustomDatePipe, CommonModule, FormsModule ,IonItem ,IonList,IonToolbar , IonLabel,  IonHeader, IonButtons ,IonMenuButton ,IonContent ,IonTitle , IonButton]
})
export class FormsPage implements OnInit {
  notifications$: Observable<NotifResult[]> | undefined;
  templates$: Observable<TEMPLATE_HEADER[]|null> | undefined;
  forms$!: Observable<FORM_HEADER[]>;
  groupedForms: { category: string, items: FORM_HEADER[] }[] = [];
  plainList: FORM_HEADER[] = [];
  categoryToggle: boolean = false; // now a simple boolean
  isTemplateLoadTriggered = false;
  searchTermSubject = new BehaviorSubject<string>('');
  filteredForms$: Observable<FORM_HEADER[]> | undefined;
  hideCompletedToggle: boolean = false;
  private myBrowser!: InAppBrowserObject;

  constructor(
    private unviredSdk: UnviredCordovaSDK,
    private store: Store,
    private routerOutlet: IonRouterOutlet,
    private menuCtrl: MenuController,
    private dataService: DataService,
    private file: File,
    private utilityService: UtilityService,
    private iab: InAppBrowser,
    private webview: WebView,
    private platform: Platform
  ) {
    this.routerOutlet.swipeGesture = false;
    this.menuCtrl.swipeGesture(true)
  }

  ngOnInit() {
    // this.store.subscribe(state => console.log('AppState:', state));


     this.store.dispatch(RigActions.loadRigFromDb());
      
        // When rig data is loaded, check if templates need to be loaded
        this.store.select(selectRigLoadedFromDb).pipe(
          tap((loaded: any) => console.log('Rig data loaded from DB:', loaded)),
          filter((loaded): loaded is boolean => !!loaded),
          switchMap(() => this.store.select(selectRigData).pipe(take(1))), // Get the rig data
          take(1)
        ).subscribe((rigData) => {
          if (!rigData || !rigData.RIG_NO) {
            console.warn('RigData is null or RIG_NO missing — opening site popup');
            // await this.openSiteNumberPopup();
          } else {
            // Dispatch action to load templates only if they're not already loaded
            this.store.dispatch(RigActions.loadAllTemplatesFromDb());
          }
        });
      
        this.store.select(selectAllNotifications).pipe(
          tap(events => {
            // console.log('Current notifications:', events);
            // Find the first event of type 4
            const firstEventType4 = events.find(event => event.type === 4);
            
            if (firstEventType4 && !this.isTemplateLoadTriggered) {
              // Only dispatch the action if we haven't already triggered the DB call
              console.log('First event with type 4 found:', firstEventType4);
              this.store.dispatch(RigActions.loadAllTemplatesFromDb());
             
             
             
                 this.templates$ = this.store.select(selectAllTemplates).pipe(
                   tap(loaded => {
                    
                     console.log('Templates loaded from DB:', loaded);
                    //  this.isLoading = !loaded;  // Set loading state based on the `loaded` flag
                   })
                 )
                 this.isTemplateLoadTriggered = true;
            }
          })
        ).subscribe();
      
    // this.notifications$ = this.store.select(selectAllNotifications).pipe(distinctUntilChanged());
    
    this.store.select(selectRigLoadedFromDb).pipe(
      tap((loaded: any) => console.log('Rig data loaded from DB:', loaded)),
      filter((loaded): loaded is boolean => !!loaded),
      switchMap(() => this.store.select(selectRigData).pipe(take(1))),
      take(1)
    ).subscribe((rigData) => {
      if (!rigData || !rigData.RIG_NO) {
        console.warn('RigData is null or RIG_NO missing — opening site popup');
        // await this.openSiteNumberPopup();
      } else {
        // Only execute forms logic if rigNo is present
        this.store.dispatch(RigActions.loadAllFormsFromDb());
        this.forms$ = this.store.select(selectAllForms);
        this.forms$.subscribe((forms: FORM_HEADER[]) => {
          this.plainList = forms;
          this.updateGroupedForms();
        });
        this.filteredForms$ = this.searchTermSubject.asObservable().pipe(
          switchMap(term =>
            this.forms$.pipe(
              map(forms =>
                forms.filter(form =>
                  (form.TEMPLATE_DESC?.toLowerCase().includes(term) ||
                   form.FORM_ID?.toLowerCase().includes(term) ||
                   form.CATEGORY_DESC?.toLowerCase().includes(term))
                )
              )
            )
          )
        );
      }
    });
  }

  updateGroupedForms() {
    if (this.categoryToggle) {
      const groups: { [key: string]: FORM_HEADER[] } = {};
      this.plainList.forEach(form => {
        const cat = form.CATEGORY_DESC ? form.CATEGORY_DESC : '';
        if (!groups[cat]) {
          groups[cat] = [];
        }
        groups[cat].push(form);
      });
      this.groupedForms = Object.entries(groups).map(([category, items]) => ({ category, items }));
    } else {
      this.groupedForms = [{ category: 'Forms', items: this.plainList }];
    }
  }

  async getForms(){
    console.log('send log', this.plainList , this.categoryToggle )
    await this.unviredSdk.sendLogToServer()
     this.forms$ = this.store.select(selectAllForms)
     this.forms$.subscribe((forms: FORM_HEADER[]) => {
      this.plainList = forms;
      console.log('this.forms is ' , forms)
      this.updateGroupedForms();
    })
    //  this.categoryToggle$.subscribe(val => {
  // console.log('Toggle value:', val);
    // loading$ = this.store.select(selectFormsLoading);
    // error$ = this.store.select(selectFormsError);
  }
  

  onCategoryToggle(event: any) {
    this.categoryToggle = event.detail.checked;
    this.updateGroupedForms();
  }


   onHideCompletedToggle(event: any) {
    this.hideCompletedToggle = event.detail.checked;
    this.applyFilters();
  }

  applyFilters() {
    let filtered = this.plainList;
    if (this.hideCompletedToggle) {
      filtered = filtered.filter(form => form.FORM_STATUS !== 'SUBM');
    }
    if (this.searchTermSubject) {
      const term = this.searchTermSubject.getValue();
      if (term) {
        filtered = filtered.filter(form =>
          (form.TEMPLATE_DESC?.toLowerCase().includes(term) ||
           form.FORM_ID?.toLowerCase().includes(term) ||
           form.CATEGORY_DESC?.toLowerCase().includes(term))
        );
      }
    }
    this.plainList = filtered;
    this.updateGroupedForms();
  }

    onSearchChange(term: string | null | undefined) {
      const searchValue = term?.toLowerCase() ?? '';
      this.searchTermSubject.next(searchValue);
      if (searchValue === '') {
        // If search is cleared, show all forms based on current filter
        this.forms$.subscribe((forms: FORM_HEADER[]) => {
          let filtered = forms;
          if (this.hideCompletedToggle) {
            filtered = filtered.filter(form => form.FORM_STATUS !== 'SUBM');
          }
          this.plainList = filtered;
          this.updateGroupedForms();
        });
      } else {
        // Otherwise, filter by search term
        this.filteredForms$?.subscribe(filtered => {
          this.plainList = filtered;
          this.updateGroupedForms();
        });
      }
    }

  async onFormSelect(form: FORM_HEADER) {
    console.log('Form selected:', form);
    try {
      const pathResult: any = await this.unviredSdk.getAttachmentFolderPath();
      console.log('Path result from SDK:', pathResult);
      if (!pathResult ) {
        console.error('Could not retrieve attachment folder path.');
        return;
      }

      const attachmentPath = pathResult; // Extract the actual path
      const folderName = form.VER_ID;
      const zipFileName = `${form.VER_ID}.zip`;
      console.log('Attachment path:', attachmentPath);
      console.log('Folder name:', folderName);

      try {
        await this.file.checkDir(attachmentPath, folderName);
        console.log('Folder exists in path.');
        this.unviredSdk.logInfo('FormsPage', 'onFormSelect', `Folder ${folderName} exists in path ${attachmentPath}`)

        // Check for index.html file in the existing folder
        try {
          const folderPath = `${attachmentPath}${folderName}/`;
          console.log('Checking for index.html in path:', folderPath);
          let indexExists = await this.file.checkFile(folderPath, 'index.html');
          console.log('indexExists:', indexExists);
          console.log('index.html file exists in folder.');
          this.unviredSdk.logInfo('FormsPage', 'onFormSelect', `index.html found in folder ${folderName}`);

          // Display the index.html file using InAppBrowser
          const indexHtmlPath = `${folderPath}index.html`;
          console.log('Full index.html path:', indexHtmlPath);
          this.openFileInBrowser(indexHtmlPath);

        } catch (fileError) {
          console.log('File check error:', fileError);
          this.unviredSdk.logInfo('FormsPage', 'onFormSelect', `index.html not found in folder ${folderName}`);
          console.log(`index.html file does not exist in folder '${folderName}'.`);
        }

      } catch (dirError) {
        console.log(`Folder '${folderName}' does not exist. Creating directory...`);
        try {
          await this.file.createDir(attachmentPath, folderName, false);
          // console.log(`Directory '${folderName}' created in path '${attachmentPath}'.`);
        } catch (createError) {
          this.unviredSdk.logError('FormsPage', 'onFormSelect', `Failed to create directory '${folderName}'`)
        }
        try {
          const attachmentAsArrayBuffer = await this.utilityService.getAttachmentAsArrayBuffer(attachmentPath+'/'+zipFileName)
          await this.utilityService.unzipTheFileAndWriteFileContent(attachmentAsArrayBuffer , folderName , attachmentPath )

        } catch (zipError) {
          this.unviredSdk.logInfo('FormsPage','onFormSelect',`Neither folder nor ZIP file exists in path ${attachmentPath}.`)
        }
      }
    } catch (error) {
      console.error('Error getting attachment folder path:', error);
    }
  }

  /**
   * Opens the HTML file using InAppBrowser based on platform
   */
  // private openFileInBrowser(filePath: string) {
  //   if (this.platform.is('electron')) {
  //     const { shell } = (window as any).require('electron');
  //     shell.openPath(filePath);
  //   } else if (this.platform.is('ios')) {
  //     console.log('Opening file in iOS browser:', filePath);
  //     const urlEncodedPath = encodeURI(filePath);
  //     this.viewFileInAppBrowser(urlEncodedPath);
  //   } else {
  //     // For other platforms (Android, etc.)
  //     const urlEncodedPath = encodeURI(filePath);
  //     this.viewFileInAppBrowser(urlEncodedPath);
  //   }
  // }
  openFileInBrowser(filePath: string) {
  const url = this.webview.convertFileSrc(filePath);
  this.iab.create(url, '_blank', { location: 'no' });
}

  /**
   * Creates and displays the InAppBrowser instance
   */
  private viewFileInAppBrowser(urlEncodedPath: string) {
    this.myBrowser = this.iab.create(urlEncodedPath, '_blank', {
      location: 'no',
      clearcache: 'yes',
      clearsessioncache: 'yes',
      fullscreen: 'yes',
      hardwareback: 'no',
      toolbar: 'no'
    });

    this.myBrowser.on('loadstop').subscribe(() => {
      this.myBrowser.show();
    });

    this.myBrowser.on('loaderror').subscribe((error) => {
      console.error('Error loading file in browser:', error);
      this.unviredSdk.logError('FormsPage', 'viewFileInAppBrowser', `Error loading file: ${JSON.stringify(error)}`);
    });
  }
}
